{"_from": "set-cookie-parser@^2.6.0", "_id": "set-cookie-parser@2.7.1", "_inBundle": false, "_integrity": "sha512-IOc8uWeOZgnb3ptbCURJWNjWUPcO3ZnTTdzsurqERrP6nPyv+paC55vJM0LpOlT2ne+Ix+9+CRG1MNLlyZ4GjQ==", "_location": "/set-cookie-parser", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "set-cookie-parser@^2.6.0", "name": "set-cookie-parser", "escapedName": "set-cookie-parser", "rawSpec": "^2.6.0", "saveSpec": null, "fetchSpec": "^2.6.0"}, "_requiredBy": ["/react-router"], "_resolved": "https://registry.npmjs.org/set-cookie-parser/-/set-cookie-parser-2.7.1.tgz", "_shasum": "3016f150072202dfbe90fadee053573cc89d2943", "_spec": "set-cookie-parser@^2.6.0", "_where": "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\node_modules\\react-router", "author": {"name": "<PERSON>", "url": "http://nfriedly.com/"}, "bugs": {"url": "https://github.com/nfriedly/set-cookie-parser/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Parses set-cookie headers into objects", "devDependencies": {"eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^9.0.11", "mocha": "^10.3.0", "prettier": "^3.2.5", "pretty-quick": "^4.0.0", "sinon": "^17.0.1"}, "files": ["lib"], "homepage": "https://github.com/nfriedly/set-cookie-parser", "keywords": ["set-cookie", "set", "cookie", "cookies", "header", "parse", "parser"], "license": "MIT", "main": "./lib/set-cookie.js", "name": "set-cookie-parser", "prettier": {"trailingComma": "es5"}, "repository": {"type": "git", "url": "git+https://github.com/nfriedly/set-cookie-parser.git"}, "scripts": {"autofix": "npm run lint -- --fix", "lint": "eslint . --ignore-pattern '!.eslintrc.js'", "precommit": "npm test", "test": "npm run lint && mocha"}, "sideEffects": false, "version": "2.7.1"}
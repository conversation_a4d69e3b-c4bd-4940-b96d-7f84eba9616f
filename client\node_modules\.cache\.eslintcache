[{"C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\BoardViewer.js": "4", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Dashboard\\UserDashboard.js": "5", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Auth\\Login.js": "6", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Auth\\UserContext.js": "7", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\Leaderboard.js": "8", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\BingoBoard.js": "9", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\PortraitOverlay.js": "10", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\BingoSquare.js": "11", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\PointsDisplay.js": "12", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\ConfirmationModal.js": "13", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\HowToPlay\\HowToPlay.js": "14"}, {"size": 535, "mtime": 1747521110957, "results": "15", "hashOfConfig": "16"}, {"size": 4379, "mtime": 1749095653126, "results": "17", "hashOfConfig": "16"}, {"size": 362, "mtime": 1747521110970, "results": "18", "hashOfConfig": "16"}, {"size": 3819, "mtime": 1749093515247, "results": "19", "hashOfConfig": "16"}, {"size": 9753, "mtime": 1749093515268, "results": "20", "hashOfConfig": "16"}, {"size": 4136, "mtime": 1749093515222, "results": "21", "hashOfConfig": "16"}, {"size": 4385, "mtime": 1747885973072, "results": "22", "hashOfConfig": "16"}, {"size": 3167, "mtime": 1749093515257, "results": "23", "hashOfConfig": "16"}, {"size": 9947, "mtime": 1747885973079, "results": "24", "hashOfConfig": "16"}, {"size": 5161, "mtime": 1749093515266, "results": "25", "hashOfConfig": "16"}, {"size": 4275, "mtime": 1749093515233, "results": "26", "hashOfConfig": "16"}, {"size": 3915, "mtime": 1747885973113, "results": "27", "hashOfConfig": "16"}, {"size": 745, "mtime": 1747885973100, "results": "28", "hashOfConfig": "16"}, {"size": 6253, "mtime": 1749095675795, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10mvb5b", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\BoardViewer.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Dashboard\\UserDashboard.js", ["72"], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Auth\\Login.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Auth\\UserContext.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\Leaderboard.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\BingoBoard.js", ["73"], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\PortraitOverlay.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\BingoSquare.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\PointsDisplay.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\ConfirmationModal.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\HowToPlay\\HowToPlay.js", [], [], {"ruleId": "74", "severity": 1, "message": "75", "line": 107, "column": 9, "nodeType": "76", "messageId": "77", "endLine": 107, "endColumn": 27}, {"ruleId": "78", "severity": 1, "message": "79", "line": 75, "column": 6, "nodeType": "80", "endLine": 75, "endColumn": 60, "suggestions": "81"}, "no-unused-vars", "'handleRefreshClick' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'calculateTotalPoints'. Either include it or remove the dependency array.", "ArrayExpression", ["82"], {"desc": "83", "fix": "84"}, "Update the dependencies array to be: [markedCells, isReadOnly, userId, boardId, characters, calculateTotalPoints]", {"range": "85", "text": "86"}, [2631, 2685], "[markedCells, isReadOnly, userId, boardId, characters, calculateTotalPoints]"]
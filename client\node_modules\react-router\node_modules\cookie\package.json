{"_from": "cookie@^1.0.1", "_id": "cookie@1.0.2", "_inBundle": false, "_integrity": "sha512-9Kr/j4O16ISv8zBBhJoi4bXOYNTkFLOqSL3UDB0njXxCXNezjeyVrJyGOWtgfs/q2km1gwBcfH8q1yEGoMYunA==", "_location": "/react-router/cookie", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "cookie@^1.0.1", "name": "cookie", "escapedName": "cookie", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/react-router"], "_resolved": "https://registry.npmjs.org/cookie/-/cookie-1.0.2.tgz", "_shasum": "27360701532116bd3f1f9416929d176afe1e4610", "_spec": "cookie@^1.0.1", "_where": "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\node_modules\\react-router", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "HTTP server cookie parsing and serialization", "devDependencies": {"@borderless/ts-scripts": "^0.15.0", "@vitest/coverage-v8": "^2.1.2", "top-sites": "1.1.194", "typescript": "^5.6.2", "vitest": "^2.1.2"}, "engines": {"node": ">=18"}, "files": ["dist/"], "homepage": "https://github.com/jshttp/cookie#readme", "keywords": ["cookie", "cookies"], "license": "MIT", "main": "dist/index.js", "name": "cookie", "repository": {"type": "git", "url": "git+https://github.com/jshttp/cookie.git"}, "scripts": {"bench": "vitest bench", "build": "ts-scripts build", "format": "ts-scripts format", "prepare": "ts-scripts install", "prepublishOnly": "npm run build", "specs": "ts-scripts specs", "test": "ts-scripts test"}, "ts-scripts": {"project": "tsconfig.build.json"}, "types": "dist/index.d.ts", "version": "1.0.2"}
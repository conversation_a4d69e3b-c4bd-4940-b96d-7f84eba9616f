{"_from": "react-router-dom@7.6.0", "_id": "react-router-dom@7.6.0", "_inBundle": false, "_integrity": "sha512-DYgm6RDEuKdopSyGOWZGtDfSm7Aofb8CCzgkliTjtu/eDuB0gcsv6qdFhhi8HdtmA+KHkt5MfZ5K2PdzjugYsA==", "_location": "/react-router-dom", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "react-router-dom@7.6.0", "name": "react-router-dom", "escapedName": "react-router-dom", "rawSpec": "7.6.0", "saveSpec": null, "fetchSpec": "7.6.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/react-router-dom/-/react-router-dom-7.6.0.tgz", "_shasum": "eadcede43856dc714fa3572a946fd7502775c017", "_spec": "react-router-dom@7.6.0", "_where": "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client", "author": {"name": "Remix Software", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/remix-run/react-router/issues"}, "bundleDependencies": false, "dependencies": {"react-router": "7.6.0"}, "deprecated": false, "description": "Declarative routing for React web applications", "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "tsup": "^8.3.0", "typescript": "^5.1.6", "wireit": "0.14.9"}, "engines": {"node": ">=20.0.0"}, "exports": {".": {"node": {"types": "./dist/index.d.ts", "module-sync": "./dist/index.mjs", "default": "./dist/index.js"}, "import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "./package.json": "./package.json"}, "files": ["dist/", "LICENSE.md", "README.md"], "homepage": "https://github.com/remix-run/react-router#readme", "keywords": ["react", "router", "route", "routing", "history", "link"], "license": "MIT", "main": "./dist/main.js", "module": "./dist/index.mjs", "name": "react-router-dom", "peerDependencies": {"react": ">=18", "react-dom": ">=18"}, "repository": {"type": "git", "url": "git+https://github.com/remix-run/react-router.git", "directory": "packages/react-router-dom"}, "scripts": {"build": "wireit", "typecheck": "tsc"}, "sideEffects": false, "types": "./dist/index.d.ts", "unpkg": "./dist/umd/react-router-dom.production.min.js", "version": "7.6.0", "wireit": {"build": {"command": "tsup", "files": ["*.ts", "tsconfig.json", "package.json"], "output": ["dist/**"]}}}
{"_from": "react-router@7.6.0", "_id": "react-router@7.6.0", "_inBundle": false, "_integrity": "sha512-GGufuHIVCJDbnIAXP3P9Sxzq3UUsddG3rrI3ut1q6m0FI6vxVBF3JoPQ38+W/blslLH4a5Yutp8drkEpXoddGQ==", "_location": "/react-router", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "react-router@7.6.0", "name": "react-router", "escapedName": "react-router", "rawSpec": "7.6.0", "saveSpec": null, "fetchSpec": "7.6.0"}, "_requiredBy": ["/react-router-dom"], "_resolved": "https://registry.npmjs.org/react-router/-/react-router-7.6.0.tgz", "_shasum": "e2d0872d7bea8df79465a8bba9a20c87c32ce995", "_spec": "react-router@7.6.0", "_where": "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\node_modules\\react-router-dom", "author": {"name": "Remix Software", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/remix-run/react-router/issues"}, "bundleDependencies": false, "dependencies": {"cookie": "^1.0.1", "set-cookie-parser": "^2.6.0"}, "deprecated": false, "description": "Declarative routing for React", "devDependencies": {"@types/set-cookie-parser": "^2.4.1", "react": "^18.2.0", "react-dom": "^18.2.0", "rimraf": "^6.0.1", "tsup": "^8.3.0", "typescript": "^5.1.6", "wireit": "0.14.9"}, "engines": {"node": ">=20.0.0"}, "exports": {".": {"node": {"types": "./dist/development/index.d.ts", "module-sync": "./dist/development/index.mjs", "default": "./dist/development/index.js"}, "import": {"types": "./dist/development/index.d.mts", "default": "./dist/development/index.mjs"}, "default": {"types": "./dist/development/index.d.ts", "default": "./dist/development/index.js"}}, "./route-module": {"node": {"types": "./dist/development/lib/types/route-module.d.ts"}, "import": {"types": "./dist/development/lib/types/route-module.d.mts"}, "default": {"types": "./dist/development/lib/types/route-module.d.ts"}}, "./dom": {"node": {"types": "./dist/development/dom-export.d.ts", "module-sync": "./dist/development/dom-export.mjs", "default": "./dist/development/dom-export.js"}, "import": {"types": "./dist/development/dom-export.d.mts", "default": "./dist/development/dom-export.mjs"}, "default": {"types": "./dist/development/dom-export.d.ts", "default": "./dist/development/dom-export.js"}}, "./package.json": "./package.json"}, "files": ["dist/", "CHANGELOG.md", "LICENSE.md", "README.md"], "homepage": "https://github.com/remix-run/react-router#readme", "keywords": ["react", "router", "route", "routing", "history", "link"], "license": "MIT", "main": "./dist/development/index.js", "module": "./dist/development/index.mjs", "name": "react-router", "peerDependencies": {"react": ">=18", "react-dom": ">=18"}, "peerDependenciesMeta": {"react-dom": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/remix-run/react-router.git", "directory": "packages/react-router"}, "scripts": {"build": "wireit", "typecheck": "tsc"}, "sideEffects": false, "types": "./dist/development/index.d.ts", "version": "7.6.0", "wireit": {"build": {"command": "rimraf dist && tsup", "files": ["lib/**", "*.ts", "tsconfig.json", "package.json"], "output": ["dist/**"]}}}